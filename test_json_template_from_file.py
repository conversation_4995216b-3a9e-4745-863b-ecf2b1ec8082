#!/usr/bin/env python3
"""
Test script cho JSON Template API sử dụng dữ liệu từ test.text
"""

import json
import requests
import sys
import os

# API endpoint
BASE_URL = "http://localhost:8000"
API_ENDPOINT = f"{BASE_URL}/api/v1/slides/process-json-template"

def load_test_data():
    """Load test data từ file test.text"""
    try:
        with open("test.text", "r", encoding="utf-8") as f:
            data = json.load(f)
        return data
    except FileNotFoundError:
        print("❌ File test.text không tìm thấy!")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Lỗi parse JSON: {e}")
        return None

def test_json_template_api():
    """Test JSON template processing API với dữ liệu từ file"""
    
    print("🧪 Testing JSON Template API với dữ liệu từ test.text...")
    print(f"📡 Endpoint: {API_ENDPOINT}")
    
    # Load test data
    request_data = load_test_data()
    if not request_data:
        return False
    
    print(f"📋 Request data loaded:")
    print(f"  - Lesson ID: {request_data.get('lesson_id')}")
    print(f"  - Template slides: {len(request_data.get('template', {}).get('slides', []))}")
    print(f"  - Config prompt: {request_data.get('config_prompt', 'None')}")
    
    # Thêm config prompt nếu chưa có
    if 'config_prompt' not in request_data:
        request_data['config_prompt'] = "Tạo slide về môn Hóa học lớp 10, chủ đề nguyên tử và nguyên tố hóa học"
    
    try:
        # Send POST request
        print("\n🔄 Sending request...")
        response = requests.post(
            API_ENDPOINT,
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=120  # Tăng timeout vì có thể mất thời gian
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"📋 Response data:")
            print(f"  - Success: {result.get('success')}")
            print(f"  - Lesson ID: {result.get('lesson_id')}")
            print(f"  - Slides created: {result.get('slides_created')}")
            
            if result.get('processed_template'):
                processed_slides = result['processed_template'].get('slides', [])
                print(f"  - Processed slides: {len(processed_slides)}")
                
                # Show first few slides as examples
                for i, slide in enumerate(processed_slides[:3]):
                    print(f"\n📄 Slide {i+1} example:")
                    print(f"  - ID: {slide.get('id')}")
                    print(f"  - Title: {slide.get('title')}")
                    print(f"  - Elements: {len(slide.get('elements', []))}")
                    
                    # Show first element content
                    elements = slide.get('elements', [])
                    if elements:
                        first_element = elements[0]
                        text = first_element.get('text', '')
                        print(f"  - First element text: {text[:100]}...")
                
                # Save result to file
                output_file = "json_template_result_from_test.json"
                with open(output_file, "w", encoding="utf-8") as f:
                    json.dump(result, f, indent=2, ensure_ascii=False)
                print(f"\n💾 Result saved to: {output_file}")
                
            return True
            
        else:
            print("❌ Request failed!")
            try:
                error_data = response.json()
                print(f"📋 Error details:")
                print(f"  - Error code: {error_data.get('error_code')}")
                print(f"  - Error message: {error_data.get('error_message')}")
            except:
                print(f"📋 Raw response: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ Request timeout! Server có thể đang xử lý...")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ Connection error! Kiểm tra server có đang chạy không.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def analyze_template_structure():
    """Phân tích cấu trúc template từ test data"""
    print("\n🔍 Analyzing template structure...")
    
    data = load_test_data()
    if not data:
        return
    
    template = data.get('template', {})
    slides = template.get('slides', [])
    
    print(f"📊 Template Analysis:")
    print(f"  - Version: {template.get('version')}")
    print(f"  - Format: {template.get('slideFormat')}")
    print(f"  - Total slides: {len(slides)}")
    
    # Analyze placeholder types in each slide
    placeholder_stats = {}
    
    for i, slide in enumerate(slides):
        slide_id = slide.get('id')
        elements = slide.get('elements', [])
        
        print(f"\n📄 Slide {i+1} ({slide_id}):")
        print(f"  - Elements: {len(elements)}")
        
        slide_placeholders = {}
        for element in elements:
            text = element.get('text', '')
            # Extract placeholder type from text like "TitleName 70"
            if ' ' in text:
                placeholder_type = text.split()[0]
                slide_placeholders[placeholder_type] = slide_placeholders.get(placeholder_type, 0) + 1
                placeholder_stats[placeholder_type] = placeholder_stats.get(placeholder_type, 0) + 1
        
        if slide_placeholders:
            print(f"  - Placeholders: {slide_placeholders}")
    
    print(f"\n📈 Overall placeholder statistics:")
    for placeholder_type, count in sorted(placeholder_stats.items()):
        print(f"  - {placeholder_type}: {count} occurrences")

if __name__ == "__main__":
    print("🚀 JSON Template API Test với dữ liệu từ test.text")
    print("=" * 60)
    
    # Analyze template first
    analyze_template_structure()
    
    print("\n" + "=" * 60)
    
    # Run API test
    success = test_json_template_api()
    
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    if success:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed!")
        sys.exit(1)
